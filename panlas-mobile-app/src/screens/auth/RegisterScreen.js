import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useAuth } from '../../context/AuthContext';
import { colors, legacyFonts, legacySpacing, borderRadius } from '../../styles/theme';
import PasswordStrengthIndicator from '../../components/PasswordStrengthIndicator';
import EmailValidator from '../../components/EmailValidator';
// TermsModal removed - will be handled during first login

const RegisterScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    dateOfBirth: '',
    gender: '',
    barangay: '',
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showGenderPicker, setShowGenderPicker] = useState(false);
  const [showBarangayPicker, setShowBarangayPicker] = useState(false);
  const [showEmailValidation, setShowEmailValidation] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  // Terms will be handled during first login, not registration

  const { register } = useAuth();

  // Helper function to calculate age
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Gender options that match backend enum values
  const genderOptions = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
    { label: 'Prefer not to say', value: 'prefer not to say' },
  ];

  // Barangay options matching the web app
  const barangayOptions = [
    { label: 'Barangay Aguho', value: 'Barangay Aguho' },
    { label: 'Barangay Magtanggol', value: 'Barangay Magtanggol' },
    { label: 'Barangay Martires Del 96', value: 'Barangay Martires Del 96' },
    { label: 'Barangay Poblacion', value: 'Barangay Poblacion' },
    { label: 'Barangay San Pedro', value: 'Barangay San Pedro' },
    { label: 'Barangay San Roque', value: 'Barangay San Roque' },
    { label: 'Barangay Santa Ana', value: 'Barangay Santa Ana' },
    { label: 'Barangay Santo Rosario-Kanluran', value: 'Barangay Santo Rosario-Kanluran' },
    { label: 'Barangay Santo Rosario-Silangan', value: 'Barangay Santo Rosario-Silangan' },
    { label: 'Barangay Tabacalera', value: 'Barangay Tabacalera' },
  ];

  const handleRegister = async () => {
    // Validation
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    // Required fields validation (DOB, Gender, Barangay)
    if (!formData.dateOfBirth || !formData.gender || !formData.barangay) {
      Alert.alert('Error', 'Please fill in all required fields including Date of Birth, Gender, and Barangay');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    if (formData.password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return;
    }

    // Age validation
    const age = calculateAge(formData.dateOfBirth);
    if (age === null) {
      Alert.alert('Error', 'Please enter a valid date of birth');
      return;
    }

    if (age < 13) {
      Alert.alert('Age Restriction', 'You must be at least 13 years old to register for PanlasApp');
      return;
    }

    if (age > 120) {
      Alert.alert('Invalid Age', 'Please enter a valid date of birth');
      return;
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      Alert.alert('Error', 'Enter a valid email address.');
      return;
    }

    // Gmail domain validation
    const gmailRegex = /^[^\s@]+@gmail\.com$/i;
    if (!gmailRegex.test(formData.email)) {
      Alert.alert('Error', 'Email must be a Gmail address.');
      return;
    }

    setLoading(true);
    try {
      // Register the user directly (terms will be handled during first login)
      const result = await register({
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
        username: formData.username.trim() || formData.email.trim(),
        dateOfBirth: formData.dateOfBirth,
        gender: formData.gender,
        barangay: formData.barangay.trim(),
      });

      if (result.success) {
        if (result.data.requiresVerification) {
          Alert.alert(
            'Registration Successful!',
            'Please check your email for a verification code to complete your registration.',
            [
              {
                text: 'OK',
                onPress: () => navigation.navigate('OTPVerification', {
                  email: formData.email.trim().toLowerCase()
                })
              }
            ]
          );
        } else {
          Alert.alert('Registration Successful!', 'Welcome to PanlasApp!');
        }
      } else {
        // Handle registration errors
        if (result.userExists || result.redirectToLogin) {
          const errorMessage = result.isEmailVerified
            ? 'An account with this email already exists and is verified. Please sign in instead.'
            : 'An account with this email exists but is not verified. Please check your email for verification or try logging in.';

          Alert.alert(
            'Account Already Exists',
            errorMessage,
            [
              {
                text: 'Cancel',
                style: 'cancel'
              },
              {
                text: result.isEmailVerified ? 'Go to Login' : 'Try Login',
                onPress: () => navigation.navigate('Login', {
                  prefillEmail: formData.email.trim().toLowerCase()
                })
              }
            ]
          );
        } else {
          Alert.alert(
            'Registration Failed',
            result.error || 'Unable to create account. Please check your information and try again.',
            [
              {
                text: 'OK',
                style: 'default'
              }
            ]
          );
        }
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
      setLoading(false);
    }
  };

  const [fieldErrors, setFieldErrors] = useState({});

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    validateFieldRealTime(field, value);
  };

  const validateFieldRealTime = (fieldName, value) => {
    const newErrors = { ...fieldErrors };

    switch (fieldName) {
      case 'firstName':
        if (!value.trim()) {
          newErrors.firstName = 'First name is required';
        } else if (value.trim().length < 2) {
          newErrors.firstName = 'First name must be at least 2 characters';
        } else {
          delete newErrors.firstName;
        }
        break;

      case 'lastName':
        if (!value.trim()) {
          newErrors.lastName = 'Last name is required';
        } else if (value.trim().length < 2) {
          newErrors.lastName = 'Last name must be at least 2 characters';
        } else {
          delete newErrors.lastName;
        }
        break;

      case 'email':
        if (!value) {
          newErrors.email = 'Email is required';
        } else if (!/^[^\s@]+@gmail\.com$/i.test(value)) {
          newErrors.email = 'Email must be gmail';
        } else {
          delete newErrors.email;
        }
        break;

      case 'password':
        if (!value) {
          newErrors.password = 'Password is required';
        } else if (value.length < 6) {
          newErrors.password = 'Password must be at least 6 characters';
        } else {
          delete newErrors.password;
        }
        break;

      case 'confirmPassword':
        if (!value) {
          newErrors.confirmPassword = 'Please confirm your password';
        } else if (formData.password !== value) {
          newErrors.confirmPassword = 'Passwords do not match';
        } else {
          delete newErrors.confirmPassword;
        }
        break;

      default:
        break;
    }

    setFieldErrors(newErrors);
  };

  // Terms acceptance handlers removed - will be handled during first login

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Account</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.welcomeSection}>
          <Text style={styles.title}>Join Panlas!</Text>
          <Text style={styles.subtitle}>Create your account to start planning delicious Filipino meals</Text>
        </View>

        <View style={styles.formSection}>
          {/* Name Fields */}
          <View style={styles.row}>
            <View style={[styles.inputContainer, styles.halfWidth]}>
              <Text style={styles.inputLabel}>First Name *</Text>
              <TextInput
                style={[styles.input, fieldErrors.firstName && styles.inputError]}
                placeholder="Enter first name"
                value={formData.firstName}
                onChangeText={(text) => updateFormData('firstName', text)}
                autoCapitalize="words"
              />
              {fieldErrors.firstName && (
                <Text style={styles.errorText}>{fieldErrors.firstName}</Text>
              )}
            </View>
            <View style={[styles.inputContainer, styles.halfWidth]}>
              <Text style={styles.inputLabel}>Last Name *</Text>
              <TextInput
                style={[styles.input, fieldErrors.lastName && styles.inputError]}
                placeholder="Enter last name"
                value={formData.lastName}
                onChangeText={(text) => updateFormData('lastName', text)}
                autoCapitalize="words"
              />
              {fieldErrors.lastName && (
                <Text style={styles.errorText}>{fieldErrors.lastName}</Text>
              )}
            </View>
          </View>

          {/* Email */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email Address *</Text>
            <TextInput
              style={[styles.input, fieldErrors.email && styles.inputError]}
              placeholder="Enter your email"
              value={formData.email}
              onChangeText={(text) => {
                updateFormData('email', text);
                setShowEmailValidation(text.length > 0);
              }}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
            {fieldErrors.email && (
              <Text style={styles.errorText}>{fieldErrors.email}</Text>
            )}
            <EmailValidator
              email={formData.email}
              showValidation={showEmailValidation}
            />
          </View>

          {/* Username */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Username</Text>
            <TextInput
              style={styles.input}
              placeholder="Choose a username (optional)"
              value={formData.username}
              onChangeText={(text) => updateFormData('username', text)}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* Password */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password *</Text>
            <View style={[styles.passwordContainer, fieldErrors.password && styles.inputError]}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Create a password"
                value={formData.password}
                onChangeText={(text) => updateFormData('password', text)}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Ionicons
                  name={showPassword ? "eye-off" : "eye"}
                  size={20}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
            {fieldErrors.password && (
              <Text style={styles.errorText}>{fieldErrors.password}</Text>
            )}
            <PasswordStrengthIndicator password={formData.password} />
          </View>

          {/* Confirm Password */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Confirm Password *</Text>
            <View style={[styles.passwordContainer, fieldErrors.confirmPassword && styles.inputError]}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChangeText={(text) => updateFormData('confirmPassword', text)}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <Ionicons
                  name={showConfirmPassword ? "eye-off" : "eye"}
                  size={20}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
            {fieldErrors.confirmPassword && (
              <Text style={styles.errorText}>{fieldErrors.confirmPassword}</Text>
            )}
          </View>

          {/* Required Personal Information */}
          <View style={styles.requiredSection}>
            <Text style={styles.sectionTitle}>Personal Information</Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Date of Birth *</Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={[styles.pickerText, !formData.dateOfBirth && styles.placeholderText]}>
                  {formData.dateOfBirth || 'Select Date of Birth'}
                </Text>
                <Ionicons name="calendar-outline" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Gender *</Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => setShowGenderPicker(true)}
              >
                <Text style={[styles.pickerText, !formData.gender && styles.placeholderText]}>
                  {formData.gender ? genderOptions.find(option => option.value === formData.gender)?.label : 'Select Gender'}
                </Text>
                <Ionicons name="chevron-down" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Barangay *</Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => setShowBarangayPicker(true)}
              >
                <Text style={[styles.pickerText, !formData.barangay && styles.placeholderText]}>
                  {formData.barangay ? barangayOptions.find(option => option.value === formData.barangay)?.label : 'Select Barangay'}
                </Text>
                <Ionicons name="chevron-down" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Register Button */}
          <TouchableOpacity
            style={[styles.registerButton, loading && styles.disabledButton]}
            onPress={handleRegister}
            disabled={loading}
          >
            <Text style={styles.registerButtonText}>
              {loading ? 'Creating Account...' : 'Create Account'}
            </Text>
          </TouchableOpacity>

          {/* Login Link */}
          <View style={styles.loginSection}>
            <Text style={styles.loginText}>Already have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={styles.loginLink}>Sign In</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Gender Picker Modal */}
      <Modal
        visible={showGenderPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowGenderPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Gender</Text>
              <TouchableOpacity
                onPress={() => setShowGenderPicker(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            {genderOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.modalOption,
                  formData.gender === option.value && styles.selectedOption
                ]}
                onPress={() => {
                  updateFormData('gender', option.value);
                  setShowGenderPicker(false);
                }}
              >
                <Text style={[
                  styles.modalOptionText,
                  formData.gender === option.value && styles.selectedOptionText
                ]}>
                  {option.label}
                </Text>
                {formData.gender === option.value && (
                  <Ionicons name="checkmark" size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </Modal>

      {/* Barangay Picker Modal */}
      <Modal
        visible={showBarangayPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowBarangayPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Barangay</Text>
              <TouchableOpacity
                onPress={() => setShowBarangayPicker(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            {barangayOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.modalOption,
                  formData.barangay === option.value && styles.selectedOption
                ]}
                onPress={() => {
                  updateFormData('barangay', option.value);
                  setShowBarangayPicker(false);
                }}
              >
                <Text style={[
                  styles.modalOptionText,
                  formData.barangay === option.value && styles.selectedOptionText
                ]}>
                  {option.label}
                </Text>
                {formData.barangay === option.value && (
                  <Ionicons name="checkmark" size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </Modal>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={selectedDate}
          mode="date"
          display="default"
          onChange={(event, date) => {
            setShowDatePicker(false);
            if (date) {
              const formattedDate = date.toISOString().split('T')[0];
              setSelectedDate(date);
              updateFormData('dateOfBirth', formattedDate);
            }
          }}
          maximumDate={new Date()}
          minimumDate={new Date(1900, 0, 1)}
        />
      )}

      {/* Terms modal removed - will be handled during first login */}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: legacySpacing.sm,
  },
  headerTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: legacySpacing.lg,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: legacySpacing.xl,
  },
  title: {
    fontSize: legacyFonts.sizes.xxlarge,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  subtitle: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  formSection: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputContainer: {
    marginBottom: legacySpacing.md,
  },
  halfWidth: {
    width: '48%',
  },
  inputLabel: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    fontSize: legacyFonts.sizes.medium,
    backgroundColor: colors.surface,
    color: colors.text,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    backgroundColor: colors.surface,
  },
  passwordInput: {
    flex: 1,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    fontSize: legacyFonts.sizes.medium,
    color: colors.text,
  },
  eyeButton: {
    padding: legacySpacing.md,
  },
  optionalSection: {
    marginTop: legacySpacing.lg,
    marginBottom: legacySpacing.lg,
  },
  sectionTitle: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: legacySpacing.md,
  },
  registerButton: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    borderRadius: borderRadius.medium,
    alignItems: 'center',
    marginTop: legacySpacing.lg,
  },
  registerButtonText: {
    color: colors.surface,
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  loginSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: legacySpacing.lg,
    marginBottom: legacySpacing.xl,
  },
  loginText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
  },
  loginLink: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.primary,
    fontWeight: 'bold',
  },
  // Picker styles
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    backgroundColor: colors.surface,
  },
  pickerText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.text,
  },
  placeholderText: {
    color: colors.textSecondary,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.large,
    padding: legacySpacing.lg,
    width: '80%',
    maxWidth: 300,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: legacySpacing.md,
  },
  modalTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCloseButton: {
    padding: legacySpacing.sm,
  },
  modalOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.sm,
    borderRadius: borderRadius.medium,
  },
  selectedOption: {
    backgroundColor: colors.primaryLight || colors.primary + '20',
  },
  modalOptionText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.text,
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  inputError: {
    borderColor: '#FF5252',
    borderWidth: 1,
  },
  errorText: {
    fontSize: 12,
    color: '#FF5252',
    marginTop: 4,
    marginLeft: 4,
  },
});

export default RegisterScreen;
