// Base API service for making requests to the backend

// Get API base URL from environment variables with fallback
const getApiBaseUrl = () => {
  // In production, use the environment variable
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }

  // In development, check if we're running locally
  if (import.meta.env.DEV) {
    return 'http://localhost:5000/api';
  }

  // Fallback for production if env var not set
  return '/api';
};

const API_BASE_URL = getApiBaseUrl();

// Helper function to check if user is authenticated
const isAuthenticated = () => {
  return !!localStorage.getItem('token');
};

// Main API request function with improved error handling
const apiRequest = async (endpoint, options = {}) => {
  try {
    const token = localStorage.getItem('token');
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`;
    }
    
    const config = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...(options.headers || {})
      }
    };
    
    console.log(`Making ${options.method || 'GET'} request to ${API_BASE_URL}${endpoint}`);
    console.log('Request config:', {
      method: options.method || 'GET',
      headers: config.headers,
      body: options.body
    });
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    
    console.log(`Response status: ${response.status}`);
    
    if (!response.ok) {
      let errorMessage = `API error: ${response.status}`;
      try {
        const errorData = await response.json();
        console.error('API error response:', errorData);
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        console.error('Could not parse error response as JSON');
      }
      throw new Error(errorMessage);
    }
    
    const data = await response.json();
    console.log('API response data:', data);
    return data;
  } catch (error) {
    console.error(`API request error for ${endpoint}:`, error);
    throw error;
  }
};

// Authentication services
const login = async (email, password) => {
  const response = await apiRequest('/users/login', {
    method: 'POST',
    body: JSON.stringify({ email, password })
  });

  if (response.token) {
    localStorage.setItem('token', response.token);
    if (response.user) {
      localStorage.setItem('user', JSON.stringify(response.user));
    }
  }

  return response;
};

const register = async (userData) => {
  const response = await apiRequest('/users/register', {
    method: 'POST',
    body: JSON.stringify(userData)
  });

  if (response.token) {
    localStorage.setItem('token', response.token);
    if (response.user) {
      localStorage.setItem('user', JSON.stringify(response.user));
    }
  }

  return response;
};

const logout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  
  // Clear any user-specific data
  const userId = JSON.parse(localStorage.getItem('user'))?.id;
  if (userId) {
    localStorage.removeItem(`mealPlan_${userId}`);
    localStorage.removeItem(`lockedDates_${userId}`);
    localStorage.removeItem(`mealTimes_${userId}`);
    localStorage.removeItem(`completedMeals_${userId}`);
    localStorage.removeItem(`mealPlanUserPrefs_${userId}`);
  }

  // Also clear any legacy non-user-specific data
  localStorage.removeItem('mealPlan');
  localStorage.removeItem('lockedDates');
  localStorage.removeItem('mealTimes');
  localStorage.removeItem('completedMeals');
  localStorage.removeItem('mealPlanUserPrefs');

  // Optionally redirect to login
  // window.location.href = '/login';
};

const getUserProfile = async () => {
  return apiRequest('/users/profile');
};

// MEAL PLAN FUNCTIONS
const getMeals = async () => {
  try {
    const response = await apiRequest('/meals', {
      method: 'GET'
    });
    return response;
  } catch (error) {
    console.error('Error fetching meals:', error);
    throw error;
  }
};

// Get all meal plans for the current user
const getMealPlans = async () => {
  return apiRequest('/meal-plans');
};

// Get meal plan for a specific date
const getMealPlanByDate = async (date) => {
  return apiRequest(`/meal-plans/${date}`);
};

// Create or update a meal plan
const createOrUpdateMealPlan = async (mealPlanData) => {
  return apiRequest('/meal-plans', {
    method: 'POST',
    body: JSON.stringify(mealPlanData)
  });
};

// Remove a meal from a plan
const removeMealFromPlan = async (date, mealType, mealInstanceId) => {
  return apiRequest(`/meal-plans/${date}/meals`, {
    method: 'DELETE',
    body: JSON.stringify({ mealType, mealInstanceId })
  });
};

// Mark a meal as completed
const markMealCompleted = async (date, mealType, completed = true) => {
  return apiRequest(`/meal-plans/${date}/${mealType}/complete`, {
    method: 'PUT',
    body: JSON.stringify({ completed })
  });
};

// Toggle lock status of a meal plan
const toggleLockMealPlan = async (date, isLocked) => {
  return apiRequest(`/meal-plans/${date}/lock`, {
    method: 'PUT',
    body: JSON.stringify({ isLocked })
  });
};

// Update meal times
const updateMealTimes = async (mealTimes) => {
  return apiRequest('/meal-plans/meal-times', {
    method: 'PUT',
    body: JSON.stringify({ mealTimes })
  });
};

// Save meal plan with name and date range
const saveMealPlan = async (mealPlanData) => {
  try {
    console.log('🔄 Saving meal plan:', mealPlanData);
    const response = await apiRequest('/meal-plans/save', {
      method: 'POST',
      body: JSON.stringify(mealPlanData)
    });
    console.log('✅ Meal plan saved successfully:', response);
    return response;
  } catch (error) {
    console.error('❌ Error saving meal plan:', error);
    throw error;
  }
};

// Get saved meal plans
const getSavedMealPlans = async () => {
  return apiRequest('/meal-plans/saved');
};

// Delete meal plan
const deleteMealPlan = async (date) => {
  return apiRequest(`/meal-plans/${date}`, {
    method: 'DELETE'
  });
};

// Generate meal plan
const generateMealPlan = async (preferences) => {
  return apiRequest('/meal-plans/generate', {
    method: 'POST',
    body: JSON.stringify(preferences)
  });
};

// Create meal plan from template
const createFromTemplate = async (templateId, date) => {
  console.log(`Creating meal plan from template ${templateId} for date ${date}`);
  return apiRequest('/meal-plans/from-template', {
    method: 'POST',
    body: JSON.stringify({ templateId, date })
  });
};

// Export all services
const apiService = {
  // Auth
  login,
  register,
  logout,
  isAuthenticated,
  getUserProfile,

  // Meal Plans
  getMealPlans,
  getMealPlanByDate,
  createOrUpdateMealPlan,
  saveMealPlan,
  getSavedMealPlans,
  deleteMealPlan,
  generateMealPlan,
  createFromTemplate,
  removeMealFromPlan,
  markMealCompleted,
  toggleLockMealPlan,
  updateMealTimes,
  getMeals,

  // Helper for custom API calls
  apiRequest
};

export default apiService;
