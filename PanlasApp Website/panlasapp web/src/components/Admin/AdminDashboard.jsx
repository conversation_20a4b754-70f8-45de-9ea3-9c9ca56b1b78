import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './AdminDashboard.css';
import SignupChart from '../SignupChart/SignupChart';
import RecentActivity from './RecentActivity';
// import UserActivityLog from './UserActivityLog';
// import UserRegistrationReport from './UserRegistrationReport';
import Analytics from './Analytics';
import Layout from '../Layout/Layout';

function AdminDashboard() {
  const [overview, setOverview] = useState(null);
  const [signupStats, setSignupStats] = useState(null);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [geolocationData, setGeolocationData] = useState(null);
  const [usersLoading, setUsersLoading] = useState(false);
  const [refreshLoading, setRefreshLoading] = useState(false);
  const [refreshSuccess, setRefreshSuccess] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [currentUserRole, setCurrentUserRole] = useState(null);
  const [currentUserPermissions, setCurrentUserPermissions] = useState([]);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedRole, setSelectedRole] = useState('admin');
  const navigate = useNavigate();

  // User filtering and search states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [verificationFilter, setVerificationFilter] = useState('all');
  const [userCounts, setUserCounts] = useState({
    total: 0,
    active: 0,
    disabled: 0,
    admin: 0,
    regular: 0,
    verified: 0,
    pending: 0
  });

  // Activity logs state
  const [activityLogs, setActivityLogs] = useState([]);
  const [activityLoading, setActivityLoading] = useState(false);
  const [activityFilters, setActivityFilters] = useState({
    action: 'all',
    user: '',
    dateFrom: '',
    dateTo: '',
    page: 1,
    limit: 50
  });
  const [activityStats, setActivityStats] = useState({
    totalActivities: 0,
    totalPages: 0,
    currentPage: 1
  });



  // Check if current user can manage users (not sub admin)
  const canManageUsers = () => {
    return currentUserRole !== 'sub_admin';
  };

  // Calculate user counts
  const calculateUserCounts = (usersList) => {
    const counts = {
      total: usersList.length,
      active: usersList.filter(user => user.isActive).length,
      disabled: usersList.filter(user => !user.isActive).length,
      admin: usersList.filter(user => user.isAdmin).length,
      regular: usersList.filter(user => !user.isAdmin).length,
      verified: usersList.filter(user => user.isEmailVerified).length,
      pending: usersList.filter(user => !user.isEmailVerified).length
    };
    setUserCounts(counts);
    return counts;
  };

  // Filter users based on search term and filters
  const filterUsers = (usersList, search, status, role, verification) => {
    console.log('🔍 filterUsers called with:', {
      usersListLength: usersList.length,
      search,
      status,
      role,
      verification
    });
    let filtered = usersList;

    // Search filter
    if (search.trim()) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(user =>
        user.username.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower)
      );
    }

    // Status filter
    if (status !== 'all') {
      if (status === 'active') {
        filtered = filtered.filter(user => user.isActive);
      } else if (status === 'disabled') {
        filtered = filtered.filter(user => !user.isActive);
      }
    }

    // Role filter
    if (role !== 'all') {
      if (role === 'admin') {
        filtered = filtered.filter(user => user.isAdmin);
      } else if (role === 'regular') {
        filtered = filtered.filter(user => !user.isAdmin);
      }
    }

    // Verification filter
    if (verification !== 'all') {
      if (verification === 'verified') {
        filtered = filtered.filter(user => user.isEmailVerified);
      } else if (verification === 'pending') {
        filtered = filtered.filter(user => !user.isEmailVerified);
      }
    }

    console.log('🔍 filterUsers result:', { filteredLength: filtered.length });
    return filtered;
  };

  // Update filtered users when filters change
  const updateFilteredUsers = useCallback(() => {
    const filtered = filterUsers(users, searchTerm, statusFilter, roleFilter, verificationFilter);
    setFilteredUsers(filtered);
  }, [users, searchTerm, statusFilter, roleFilter, verificationFilter]);

  // Handler functions for user management
  const handleDisableUser = async (userId, username) => {
    if (window.confirm(`Are you sure you want to disable user "${username}"? They will not be able to sign in until re-enabled.`)) {
      try {
        const token = localStorage.getItem('token');
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/disable`, {
          method: 'PUT',
          headers: {
            'x-auth-token': token
          }
        });

        if (response.ok) {
          const responseData = await response.json();
          console.log('Disable user response:', responseData);
          // Update user in local state
          setUsers(users.map(user =>
            user._id === userId ? { ...user, isActive: false, disabledAt: new Date() } : user
          ));
          alert(`User "${username}" has been disabled successfully.`);
        } else {
          const errorData = await response.json();
          console.error('Disable user error:', errorData);
          alert(`Error disabling user: ${errorData.message}`);
        }
      } catch (error) {
        console.error('Error disabling user:', error);
        alert('Error disabling user. Please try again.');
      }
    }
  };

  const handleEnableUser = async (userId, username) => {
    if (window.confirm(`Are you sure you want to enable user "${username}"?`)) {
      try {
        const token = localStorage.getItem('token');
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/enable`, {
          method: 'PUT',
          headers: {
            'x-auth-token': token
          }
        });

        if (response.ok) {
          const responseData = await response.json();
          console.log('Enable user response:', responseData);
          // Update user in local state
          setUsers(users.map(user =>
            user._id === userId ? { ...user, isActive: true, disabledAt: null } : user
          ));
          alert(`User "${username}" has been enabled successfully.`);
        } else {
          const errorData = await response.json();
          console.error('Enable user error:', errorData);
          alert(`Error enabling user: ${errorData.message}`);
        }
      } catch (error) {
        console.error('Error enabling user:', error);
        alert('Error enabling user. Please try again.');
      }
    }
  };

  const handleMakeAdmin = (userId, username) => {
    setSelectedUser({ id: userId, username });
    setShowRoleModal(true);
  };

  const confirmMakeAdmin = async () => {
    if (!selectedUser) return;

    try {
      const token = localStorage.getItem('token');
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await fetch(`${API_BASE_URL}/admin/users/${selectedUser.id}/make-admin`, {
        method: 'PUT',
        headers: {
          'x-auth-token': token,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ adminRole: selectedRole })
      });

      if (response.ok) {
        // Update user in local state
        setUsers(users.map(user =>
          user._id === selectedUser.id ? { ...user, isAdmin: true } : user
        ));
        alert(`User "${selectedUser.username}" has been promoted to ${selectedRole} successfully.`);
        setShowRoleModal(false);
        setSelectedUser(null);
        setSelectedRole('admin');
      } else {
        const errorData = await response.json();
        alert(`Error making user admin: ${errorData.message}`);
      }
    } catch (error) {
      console.error('Error making user admin:', error);
      alert('Error making user admin. Please try again.');
    }
  };

  const handleRemoveAdmin = async (userId, username) => {
    if (window.confirm(`Are you sure you want to remove admin privileges from "${username}"?`)) {
      try {
        const token = localStorage.getItem('token');
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/remove-admin`, {
          method: 'PUT',
          headers: {
            'x-auth-token': token
          }
        });

        if (response.ok) {
          // Update user in local state
          setUsers(users.map(user =>
            user._id === userId ? { ...user, isAdmin: false } : user
          ));
          alert(`Admin privileges removed from "${username}" successfully.`);
        } else {
          const errorData = await response.json();
          alert(`Error removing admin privileges: ${errorData.message}`);
        }
      } catch (error) {
        console.error('Error removing admin privileges:', error);
        alert('Error removing admin privileges. Please try again.');
      }
    }
  };

  // Enhanced refresh function with loading state and success message
  const handleRefreshData = async () => {
    try {
      setRefreshLoading(true);
      setRefreshSuccess(false);
      await fetchAdminData();
      setRefreshSuccess(true);

      // Hide success message after 2 seconds
      setTimeout(() => {
        setRefreshSuccess(false);
      }, 2000);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshLoading(false);
    }
  };

  const fetchAdminData = useCallback(async () => {
    try {
      setError(''); // Clear any previous errors
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const config = {
        headers: {
          'x-auth-token': token
        }
      };

      // First, get current user profile to determine role and permissions
      try {
        const userProfileRes = await axios.get(`${API_BASE_URL}/users/profile`, config);
        const currentUser = userProfileRes.data;

        // If user is admin, get their admin record for role and permissions
        if (currentUser.isAdmin) {
          try {
            const adminRes = await axios.get(`${API_BASE_URL}/admin/users/${currentUser._id}/admin-info`, config);
            setCurrentUserRole(adminRes.data.role || 'admin');
            setCurrentUserPermissions(adminRes.data.permissions || []);
          } catch (adminError) {
            // If admin info not found, default to basic admin
            setCurrentUserRole('admin');
            setCurrentUserPermissions(['user_management', 'analytics_view', 'system_health']);
          }
        }
      } catch (profileError) {
        console.error('Error fetching user profile:', profileError);
      }

      // Fetch system overview
      const overviewRes = await axios.get(`${API_BASE_URL}/admin/overview`, config);
      setOverview(overviewRes.data);

      // Fetch signup statistics
      const statsRes = await axios.get(`${API_BASE_URL}/admin/stats/signups`, config);
      setSignupStats(statsRes.data);

      // Fetch all users
      console.log('🔍 Fetching users from:', `${API_BASE_URL}/admin/users`);
      setUsersLoading(true);
      const usersRes = await axios.get(`${API_BASE_URL}/admin/users`, config);
      setUsersLoading(false);
      console.log('🔍 Fetched users data:', usersRes.data);
      console.log('🔍 Users array length:', usersRes.data.length);

      // Validate that we got an array
      if (!Array.isArray(usersRes.data)) {
        console.error('❌ Users data is not an array:', typeof usersRes.data);
        throw new Error('Invalid users data format received from server');
      }

      // Log each user's isActive status
      usersRes.data.forEach((user, index) => {
        console.log(`User ${index + 1}: ${user.username} - isActive: ${user.isActive} (type: ${typeof user.isActive})`);
      });

      setUsers(usersRes.data);

      // Calculate user counts and set initial filtered users
      calculateUserCounts(usersRes.data);
      const initialFiltered = filterUsers(usersRes.data, searchTerm, statusFilter, roleFilter, verificationFilter);
      console.log('🔍 Initial filtered users:', initialFiltered);
      console.log('🔍 Filtered users length:', initialFiltered.length);

      // Fallback: if filtering results in empty array but we have users, show all users
      if (initialFiltered.length === 0 && usersRes.data.length > 0) {
        console.log('⚠️ Filtering resulted in empty array, showing all users as fallback');
        setFilteredUsers(usersRes.data);
      } else {
        setFilteredUsers(initialFiltered);
      }

      // Fetch geolocation analytics
      const geoRes = await axios.get(`${API_BASE_URL}/admin/analytics/geolocation`, config);
      setGeolocationData(geoRes.data);
    } catch (err) {
      setUsersLoading(false); // Make sure to stop users loading on error
      if (err.response && err.response.status === 403) {
        setError('Access denied. Admin privileges required.');
        navigate('/home');
      } else {
        setError('Failed to load admin data. Please try again.');
        console.error('Admin dashboard error:', err);
      }
    }
  }, [navigate, searchTerm, statusFilter, roleFilter, verificationFilter]);

  // Fetch activity logs
  const fetchActivityLogs = useCallback(async () => {
    try {
      setActivityLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const queryParams = new URLSearchParams({
        page: activityFilters.page,
        limit: activityFilters.limit,
        ...(activityFilters.action !== 'all' && { action: activityFilters.action }),
        ...(activityFilters.user && { user: activityFilters.user }),
        ...(activityFilters.dateFrom && { dateFrom: activityFilters.dateFrom }),
        ...(activityFilters.dateTo && { dateTo: activityFilters.dateTo })
      });

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      console.log('🔍 Fetching activities with URL:', `${API_BASE_URL}/activity/log?${queryParams}`);

      const response = await axios.get(`${API_BASE_URL}/activity/log?${queryParams}`, {
        headers: { 'x-auth-token': token }
      });

      console.log('📊 Activity logs response:', response.data);

      if (response.data) {
        setActivityLogs(response.data.activities || []);
        setActivityStats({
          totalActivities: response.data.totalActivities || 0,
          totalPages: response.data.totalPages || 0,
          currentPage: response.data.currentPage || 1
        });

        console.log(`✅ Loaded ${response.data.activities?.length || 0} activities`);
      }
    } catch (err) {
      console.error('Error fetching activity logs:', err);
      setError('Failed to load activity logs. Please try again.');
    } finally {
      setActivityLoading(false);
    }
  }, [navigate, activityFilters]);

  useEffect(() => {
    fetchAdminData();
  }, [fetchAdminData]);

  // Fetch activity logs when filters change
  useEffect(() => {
    if (activeTab === 'activity-logs') {
      fetchActivityLogs();
    }
  }, [fetchActivityLogs, activeTab]);

  // Auto-refresh activity logs every 30 seconds when on activity logs tab
  useEffect(() => {
    let interval;
    if (activeTab === 'activity-logs') {
      interval = setInterval(() => {
        fetchActivityLogs();
      }, 30000); // 30 seconds
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [activeTab, fetchActivityLogs]);

  // Update filtered users when filters change
  useEffect(() => {
    updateFilteredUsers();
  }, [updateFilteredUsers]);

  // Set default tab based on user role
  useEffect(() => {
    if (currentUserRole === 'sub_admin') {
      // For sub admin, default to overview if they can manage users, otherwise keep current tab
      if (canManageUsers()) {
        setActiveTab('overview');
      }
    }
  }, [currentUserRole, currentUserPermissions]);

  // Helper function to get action color
  const getActionColor = (action) => {
    const colors = {
      login: { bg: '#d4edda', text: '#155724' },
      logout: { bg: '#f8d7da', text: '#721c24' },
      create_meal_plan: { bg: '#d1ecf1', text: '#0c5460' },
      update_meal_plan: { bg: '#fff3cd', text: '#856404' },
      delete_meal_plan: { bg: '#f5c6cb', text: '#721c24' },
      update_profile: { bg: '#e2e3e5', text: '#383d41' },
      create_meal: { bg: '#d4edda', text: '#155724' }
    };
    return colors[action] || { bg: '#e2e3e5', text: '#383d41' };
  };

  // Function to test API endpoints
  const testApiEndpoints = async () => {
    try {
      console.log('🧪 Testing API endpoints...');

      // Test 1: Ping endpoint (no auth)
      try {
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const pingResponse = await axios.get(`${API_BASE_URL}/activity/ping`);
        console.log('✅ Ping test passed:', pingResponse.data);
      } catch (error) {
        console.error('❌ Ping test failed:', error);
        alert('❌ Basic API connection failed. Check if backend is running on port 5000.');
        return;
      }

      // Test 2: Simple test endpoint (no auth)
      try {
        const simpleResponse = await axios.post(`${API_BASE_URL}/activity/test-simple`, {
          test: 'data'
        });
        console.log('✅ Simple test passed:', simpleResponse.data);
      } catch (error) {
        console.error('❌ Simple test failed:', error);
      }

      // Test 3: Auth test
      const token = localStorage.getItem('token');
      if (!token) {
        alert('❌ No authentication token found. Please login first.');
        return;
      }

      // Test 4: Database status
      try {
        const statusResponse = await axios.get(`${API_BASE_URL}/activity/status`, {
          headers: { 'x-auth-token': token }
        });
        console.log('✅ Database status:', statusResponse.data);
      } catch (error) {
        console.error('❌ Database status failed:', error);
      }

      alert('✅ API tests completed! Check console for details.');

    } catch (error) {
      console.error('❌ API test error:', error);
      alert('❌ API tests failed. Check console for details.');
    }
  };

  // Function to create test activities
  const createTestActivities = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('❌ No authentication token found. Please login first.');
        return;
      }

      console.log('🧪 Creating test activities...');
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.post(`${API_BASE_URL}/activity/test`, {}, {
        headers: { 'x-auth-token': token }
      });

      console.log('✅ Test activities created:', response.data);
      alert(`✅ Created ${response.data.count} test activities successfully!`);

      // Refresh activity logs
      fetchActivityLogs();
    } catch (error) {
      console.error('❌ Error creating test activities:', error);
      if (error.response?.status === 404) {
        alert('❌ 404 Error: Activity test endpoint not found. Check backend routes.');
      } else if (error.response?.status === 401) {
        alert('❌ 401 Error: Authentication failed. Please login again.');
      } else {
        alert(`❌ Failed to create test activities: ${error.response?.data?.message || error.message}`);
      }
    }
  };

  // Show dashboard immediately without full-screen loader
  // This allows users to see data as soon as it's available

  if (error) return (
    <Layout>
      <div className="error">{error}</div>
    </Layout>
  );

  return (
    <Layout>
      <div className="admin-dashboard">
        <h1>Admin Dashboard</h1>

      <div className="admin-tabs">
        {canManageUsers() && (
          <button
            className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
        )}
        {canManageUsers() && (
          <button
            className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}
            onClick={() => setActiveTab('users')}
          >
            Users
          </button>
        )}

        {canManageUsers() && (
          <button
            className={`tab-button ${activeTab === 'analytics' ? 'active' : ''}`}
            onClick={() => setActiveTab('analytics')}
          >
            User Analytics
          </button>
        )}
        {canManageUsers() && (
          <button
            className={`tab-button ${activeTab === 'geolocation' ? 'active' : ''}`}
            onClick={() => setActiveTab('geolocation')}
          >
            Geolocation Analytics
          </button>
        )}
        {canManageUsers() && (
          <button
            className={`tab-button ${activeTab === 'activity-logs' ? 'active' : ''}`}
            onClick={() => setActiveTab('activity-logs')}
          >
            User Activity Logs
          </button>
        )}
      </div>



      {activeTab === 'overview' && canManageUsers() && (
        <>
          {/* System Overview */}
          {overview && (
            <div className="dashboard-card">
              <h2>System Overview</h2>
              <div className="stats-grid">
                <div className="stat-box">
                  <h3>Total Users</h3>
                  <p className="stat-number">{overview.totalUsers}</p>
                </div>
                <div className="stat-box">
                  <h3>Total Meal Plans</h3>
                  <p className="stat-number">{overview.totalMealPlans}</p>
                </div>
                <div className="stat-box">
                  <h3>Avg. Meal Plans/User</h3>
                  <p className="stat-number">{overview.avgMealPlansPerUser}</p>
                </div>
              </div>

              <h3>Latest Signups</h3>
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Signup Date</th>
                  </tr>
                </thead>
                <tbody>
                  {overview.latestSignups.map((user, index) => (
                    <tr key={index}>
                      <td>{user.username}</td>
                      <td>{user.email}</td>
                      <td>{user.createdAt}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Recent Activity Section */}
          <RecentActivity />

          {/* Signup Statistics */}
          {signupStats && (
            <div className="dashboard-card">
              <h2>User Signup Statistics</h2>
              <div className="stats-grid">
                <div className="stat-box">
                  <h3>Today</h3>
                  <p className="stat-number">{signupStats.todaySignups}</p>
                </div>
                <div className="stat-box">
                  <h3>This Week</h3>
                  <p className="stat-number">{signupStats.weeklySignups}</p>
                </div>
                <div className="stat-box">
                  <h3>This Month</h3>
                  <p className="stat-number">{signupStats.monthlySignups}</p>
                </div>
              </div>

              <div className="chart-section">
                <SignupChart monthlyStats={signupStats.monthlyStats} />
              </div>

              <h3>Monthly Signups (Last 6 Months)</h3>
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Month</th>
                    <th>Signups</th>
                  </tr>
                </thead>
                <tbody>
                  {signupStats.monthlyStats.map((month, index) => (
                    <tr key={index}>
                      <td>{month.label}</td>
                      <td>{month.count}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}

      {activeTab === 'users' && canManageUsers() && (
        <div className="dashboard-card">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2>User Management</h2>
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <button
                className="btn-refresh"
                onClick={handleRefreshData}
                disabled={refreshLoading}
                style={{
                  background: refreshLoading ? '#ccc' : '#20C5AF',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: refreshLoading ? 'not-allowed' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '5px'
                }}
              >
                {refreshLoading ? (
                  <>
                    <div style={{
                      width: '12px',
                      height: '12px',
                      border: '2px solid #fff',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                    Refreshing...
                  </>
                ) : (
                  <>🔄 Refresh Data</>
                )}
              </button>
              {refreshSuccess && (
                <span style={{
                  color: '#20C5AF',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  animation: 'fadeIn 0.3s ease-in'
                }}>
                  ✅ Data refreshed!
                </span>
              )}
            </div>
          </div>

          {/* User Statistics */}
          <div className="user-stats-grid" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
            gap: '15px',
            marginBottom: '20px',
            padding: '15px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px'
          }}>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#333' }}>{userCounts.total}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Total</div>
            </div>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>{userCounts.active}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Active</div>
            </div>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#dc3545' }}>{userCounts.disabled}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Disabled</div>
            </div>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#007bff' }}>{userCounts.admin}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Admin</div>
            </div>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#6c757d' }}>{userCounts.regular}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Regular</div>
            </div>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#20c997' }}>{userCounts.verified}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Verified</div>
            </div>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ffc107' }}>{userCounts.pending}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Pending</div>
            </div>
          </div>

          {/* Search and Filter Controls */}
          <div className="user-filters" style={{
            display: 'grid',
            gridTemplateColumns: '2fr 1fr 1fr 1fr',
            gap: '15px',
            marginBottom: '20px',
            padding: '15px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px'
          }}>
            <div>
              <label style={{ display: 'block', marginBottom: '5px', fontSize: '14px', fontWeight: '500' }}>Search Users</label>
              <input
                type="text"
                placeholder="Search by username or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '5px', fontSize: '14px', fontWeight: '500' }}>Status</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              >
                <option value="all">All Status</option>
                <option value="active">Active ({userCounts.active})</option>
                <option value="disabled">Disabled ({userCounts.disabled})</option>
              </select>
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '5px', fontSize: '14px', fontWeight: '500' }}>Role</label>
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              >
                <option value="all">All Roles</option>
                <option value="admin">Admin ({userCounts.admin})</option>
                <option value="regular">Regular ({userCounts.regular})</option>
              </select>
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '5px', fontSize: '14px', fontWeight: '500' }}>Verification</label>
              <select
                value={verificationFilter}
                onChange={(e) => setVerificationFilter(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              >
                <option value="all">All Users</option>
                <option value="verified">Verified ({userCounts.verified})</option>
                <option value="pending">Pending ({userCounts.pending})</option>
              </select>
            </div>
          </div>

          <div style={{ marginBottom: '15px', fontSize: '14px', color: '#666' }}>
            Showing {filteredUsers.length} of {users.length} users
          </div>
          <table className="admin-table">
            <thead>
              <tr>
                <th>Username</th>
                <th>Email</th>
                <th>Signup Date</th>
                <th>Account Age (days)</th>
                <th>Status</th>
                <th>Email Verified</th>
                <th>Admin</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user, index) => (
                  <tr key={user._id || index} className={!user.isActive ? 'disabled-user' : ''}>
                    <td>{user.username}</td>
                    <td>{user.email}</td>
                    <td>{user.formattedCreatedAt}</td>
                    <td>{user.accountAge}</td>
                    <td>
                      <span className={`status-badge ${user.isActive ? 'active' : 'disabled'}`}>
                        {user.isActive ? 'Active' : 'Disabled'}
                      </span>
                    </td>
                    <td>
                      <span className={`status-badge ${user.isEmailVerified ? 'verified' : 'unverified'}`}>
                        {user.isEmailVerified ? 'Verified' : 'Unverified'}
                      </span>
                    </td>
                    <td>{user.isAdmin ? 'Yes' : 'No'}</td>
                    <td>
                      <div className="action-buttons">
                        {!user.isAdmin ? (
                          <button
                            className="btn-make-admin"
                            onClick={() => handleMakeAdmin(user._id, user.username)}
                            title="Make Admin"
                            disabled={!user.isActive}
                          >
                            Make Admin
                          </button>
                        ) : (
                          <button
                            className="btn-remove-admin"
                            onClick={() => handleRemoveAdmin(user._id, user.username)}
                            title="Remove Admin"
                          >
                            Remove Admin
                          </button>
                        )}
                        {user.isActive ? (
                          <button
                            className="btn-disable"
                            onClick={() => handleDisableUser(user._id, user.username)}
                            title="Disable User"
                          >
                            Disable
                          </button>
                        ) : (
                          <button
                            className="btn-enable"
                            onClick={() => handleEnableUser(user._id, user.username)}
                            title="Enable User"
                          >
                            Enable
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="8" style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
                    {usersLoading ? (
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '10px' }}>
                        <div className="circle-loader small primary"></div>
                        Loading users...
                      </div>
                    ) : users.length === 0 ? (
                      <div>
                        <div>No users found in the database.</div>
                        <div style={{ fontSize: '12px', marginTop: '5px', color: '#999' }}>
                          This might indicate a connection issue with the database.
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div>No users found matching the current filters.</div>
                        <div style={{ fontSize: '12px', marginTop: '5px', color: '#999' }}>
                          Total users in database: {users.length} | Try adjusting your filters above.
                        </div>
                      </div>
                    )}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* {activeTab === 'activity' && <UserActivityLog />} */}

      {/* {activeTab === 'reports' && <UserRegistrationReport />} */}

      {activeTab === 'analytics' && canManageUsers() && <Analytics />}

      {activeTab === 'geolocation' && canManageUsers() && geolocationData && (
        <div className="dashboard-card">
          <h2>Geolocation Analytics</h2>

          <div className="stats-grid">
            <div className="stat-box">
              <h3>Total Users</h3>
              <p className="stat-number">{geolocationData.totalUsers}</p>
            </div>
            <div className="stat-box">
              <h3>With Location Data</h3>
              <p className="stat-number">{geolocationData.usersWithBarangay}</p>
            </div>
            <div className="stat-box">
              <h3>Without Location Data</h3>
              <p className="stat-number">{geolocationData.usersWithoutBarangay}</p>
            </div>
          </div>

          <h3>User Distribution by Barangay</h3>
          <table className="admin-table">
            <thead>
              <tr>
                <th>Barangay</th>
                <th>User Count</th>
                <th>Recent Signups (30 days)</th>
              </tr>
            </thead>
            <tbody>
              {geolocationData.barangayDistribution.map((barangay, index) => {
                const recentSignups = geolocationData.recentSignupsByBarangay.find(
                  recent => recent._id === barangay._id
                );
                return (
                  <tr key={index}>
                    <td>{barangay._id}</td>
                    <td>{barangay.userCount}</td>
                    <td>{recentSignups ? recentSignups.recentSignups : 0}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}

      {/* User Activity Logs Section */}
      {activeTab === 'activity-logs' && canManageUsers() && (
        <div className="dashboard-card">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2>User Activity Logs</h2>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <button
                className="btn-test-api"
                onClick={testApiEndpoints}
                style={{
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontWeight: '500',
                  fontSize: '12px'
                }}
              >
                🔧 Test APIs
              </button>
              <button
                className="btn-test"
                onClick={createTestActivities}
                style={{
                  background: '#ffc107',
                  color: 'black',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontWeight: '500'
                }}
              >
                🧪 Create Test Activities
              </button>
              <button
                className="btn-refresh"
                onClick={fetchActivityLogs}
                style={{
                  background: '#20C5AF',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                🔄 Refresh Logs
              </button>
            </div>
          </div>

          {/* Activity Statistics */}
          <div className="activity-stats-grid" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '15px',
            marginBottom: '20px',
            padding: '15px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px'
          }}>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#333' }}>{activityStats.totalActivities}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Total Activities</div>
            </div>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#20C5AF' }}>{activityLogs.length}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Current Page</div>
            </div>
            <div className="stat-item" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#007bff' }}>{activityStats.totalPages}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>Total Pages</div>
            </div>
          </div>

          {/* Activity Filters */}
          <div className="activity-filters" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '15px',
            marginBottom: '20px',
            padding: '15px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px'
          }}>
            <div>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Action Type:</label>
              <select
                value={activityFilters.action}
                onChange={(e) => setActivityFilters(prev => ({ ...prev, action: e.target.value, page: 1 }))}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              >
                <option value="all">All Actions</option>
                <option value="login">Login</option>
                <option value="logout">Logout</option>
                <option value="create_meal_plan">Create Meal Plan</option>
                <option value="update_meal_plan">Update Meal Plan</option>
                <option value="delete_meal_plan">Delete Meal Plan</option>
                <option value="update_profile">Update Profile</option>
                <option value="create_meal">Create Meal</option>
              </select>
            </div>

            <div>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Search User:</label>
              <input
                type="text"
                placeholder="Username or email..."
                value={activityFilters.user}
                onChange={(e) => setActivityFilters(prev => ({ ...prev, user: e.target.value, page: 1 }))}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              />
            </div>

            <div>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Date From:</label>
              <input
                type="date"
                value={activityFilters.dateFrom}
                onChange={(e) => setActivityFilters(prev => ({ ...prev, dateFrom: e.target.value, page: 1 }))}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              />
            </div>

            <div>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Date To:</label>
              <input
                type="date"
                value={activityFilters.dateTo}
                onChange={(e) => setActivityFilters(prev => ({ ...prev, dateTo: e.target.value, page: 1 }))}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>

          {/* Activity Logs Table */}
          {activityLoading ? (
            <div className="loading-state">
              <div className="circle-loader medium primary"></div>
              <p>Loading activity logs...</p>
            </div>
          ) : activityLogs.length > 0 ? (
            <>
              <div className="filter-results-info" style={{ marginBottom: '15px' }}>
                Showing {activityLogs.length} of {activityStats.totalActivities} activities
                {activityFilters.action !== 'all' && ` • Filtered by: ${activityFilters.action}`}
                {activityFilters.user && ` • User: ${activityFilters.user}`}
              </div>

              <table className="admin-table">
                <thead>
                  <tr>
                    <th>User</th>
                    <th>Action</th>
                    <th>Description</th>
                    <th>IP Address</th>
                    <th>Date & Time</th>
                  </tr>
                </thead>
                <tbody>
                  {activityLogs.map((log, index) => (
                    <tr key={index}>
                      <td>
                        <div style={{ fontWeight: '500' }}>{log.username || 'Unknown'}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>{log.email || 'No email'}</div>
                      </td>
                      <td>
                        <span style={{
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: getActionColor(log.action).bg,
                          color: getActionColor(log.action).text
                        }}>
                          {log.action}
                        </span>
                      </td>
                      <td>{log.description || log.action}</td>
                      <td style={{ fontFamily: 'monospace', fontSize: '12px' }}>
                        {log.ipAddress || 'N/A'}
                      </td>
                      <td style={{ fontSize: '12px' }}>
                        {new Date(log.createdAt).toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Pagination */}
              {activityStats.totalPages > 1 && (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '10px',
                  marginTop: '20px',
                  padding: '15px'
                }}>
                  <button
                    onClick={() => setActivityFilters(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                    disabled={activityFilters.page <= 1}
                    style={{
                      padding: '8px 12px',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      backgroundColor: activityFilters.page <= 1 ? '#f5f5f5' : '#fff',
                      cursor: activityFilters.page <= 1 ? 'not-allowed' : 'pointer'
                    }}
                  >
                    Previous
                  </button>

                  <span style={{ margin: '0 15px', fontSize: '14px', color: '#666' }}>
                    Page {activityStats.currentPage} of {activityStats.totalPages}
                  </span>

                  <button
                    onClick={() => setActivityFilters(prev => ({ ...prev, page: Math.min(activityStats.totalPages, prev.page + 1) }))}
                    disabled={activityFilters.page >= activityStats.totalPages}
                    style={{
                      padding: '8px 12px',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      backgroundColor: activityFilters.page >= activityStats.totalPages ? '#f5f5f5' : '#fff',
                      cursor: activityFilters.page >= activityStats.totalPages ? 'not-allowed' : 'pointer'
                    }}
                  >
                    Next
                  </button>
                </div>
              )}
            </>
          ) : (
            <div className="no-results" style={{
              textAlign: 'center',
              padding: '40px',
              backgroundColor: '#f8f9fa',
              border: '2px dashed #dee2e6',
              borderRadius: '8px',
              color: '#6c757d',
              fontStyle: 'italic'
            }}>
              No activity logs found matching the current filters.
            </div>
          )}
        </div>
      )}

      {/* Role Selection Modal */}
      {showRoleModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '8px',
            minWidth: '400px',
            maxWidth: '500px'
          }}>
            <h3>Select Admin Role</h3>
            <p>Choose the admin role for user: <strong>{selectedUser?.username}</strong></p>

            <div style={{ margin: '20px 0' }}>
              <label style={{ display: 'block', marginBottom: '10px' }}>
                <input
                  type="radio"
                  value="admin"
                  checked={selectedRole === 'admin'}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  style={{ marginRight: '8px' }}
                />
                <strong>Admin</strong> - Full access to user management, analytics, meal and feedback management
              </label>

              <label style={{ display: 'block', marginBottom: '10px' }}>
                <input
                  type="radio"
                  value="sub_admin"
                  checked={selectedRole === 'sub_admin'}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  style={{ marginRight: '8px' }}
                />
                <strong>Sub Admin</strong> - Read-only dashboard access with full meal and feedback management
              </label>

              {/* <label style={{ display: 'block', marginBottom: '10px' }}>
                <input
                  type="radio"
                  value="moderator"
                  checked={selectedRole === 'moderator'}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  style={{ marginRight: '8px' }}
                />
                <strong>Moderator</strong> - Content management only
              </label> */}
            </div>

            <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
              <button
                onClick={() => {
                  setShowRoleModal(false);
                  setSelectedUser(null);
                  setSelectedRole('admin');
                }}
                style={{
                  padding: '8px 16px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  backgroundColor: 'white',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
              <button
                onClick={confirmMakeAdmin}
                style={{
                  padding: '8px 16px',
                  border: 'none',
                  borderRadius: '4px',
                  backgroundColor: '#20C5AF',
                  color: 'white',
                  cursor: 'pointer'
                }}
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </Layout>
  );
}

export default AdminDashboard;
