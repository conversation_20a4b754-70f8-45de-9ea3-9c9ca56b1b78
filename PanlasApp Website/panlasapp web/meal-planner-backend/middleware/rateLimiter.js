// Simple in-memory rate limiter (for production, use Redis)
class RateLimiter {
  constructor() {
    this.userAttempts = new Map(); // Store login attempts by user email
    this.blockedUsers = new Map(); // Store blocked users by email
    this.ipAttempts = new Map(); // Store login attempts by IP (for general protection)
    this.blockedIPs = new Map(); // Store blocked IPs (for general protection)
  }

  // Clean up old entries every hour
  cleanup() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    // Clean user attempts older than 1 hour
    for (const [email, data] of this.userAttempts.entries()) {
      if (now - data.firstAttempt > oneHour) {
        this.userAttempts.delete(email);
      }
    }

    // Clean blocked users older than 1 hour
    for (const [email, blockedUntil] of this.blockedUsers.entries()) {
      if (now > blockedUntil) {
        this.blockedUsers.delete(email);
      }
    }

    // Clean IP attempts older than 1 hour
    for (const [ip, data] of this.ipAttempts.entries()) {
      if (now - data.firstAttempt > oneHour) {
        this.ipAttempts.delete(ip);
      }
    }

    // Clean blocked IPs older than 1 hour
    for (const [ip, blockedUntil] of this.blockedIPs.entries()) {
      if (now > blockedUntil) {
        this.blockedIPs.delete(ip);
      }
    }
  }

  // Check if user is currently blocked
  isUserBlocked(email) {
    if (!email) return false;
    const blockedUntil = this.blockedUsers.get(email.toLowerCase());
    if (blockedUntil && Date.now() < blockedUntil) {
      return true;
    }
    if (blockedUntil) {
      this.blockedUsers.delete(email.toLowerCase()); // Remove expired block
    }
    return false;
  }

  // Check if IP is currently blocked (for general protection)
  isIPBlocked(ip) {
    const blockedUntil = this.blockedIPs.get(ip);
    if (blockedUntil && Date.now() < blockedUntil) {
      return true;
    }
    if (blockedUntil) {
      this.blockedIPs.delete(ip); // Remove expired block
    }
    return false;
  }

  // Record a failed login attempt for a user
  recordFailedUserAttempt(email) {
    if (!email) return false;

    const now = Date.now();
    const maxAttempts = 5;
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const blockDuration = 60 * 60 * 1000; // 1 hour
    const emailKey = email.toLowerCase();

    if (!this.userAttempts.has(emailKey)) {
      this.userAttempts.set(emailKey, {
        count: 1,
        firstAttempt: now,
        lastAttempt: now
      });
      return false; // Not blocked yet
    }

    const attempts = this.userAttempts.get(emailKey);

    // Reset if window has passed
    if (now - attempts.firstAttempt > windowMs) {
      this.userAttempts.set(emailKey, {
        count: 1,
        firstAttempt: now,
        lastAttempt: now
      });
      return false;
    }

    // Increment attempt count
    attempts.count++;
    attempts.lastAttempt = now;

    // Block if too many attempts
    if (attempts.count >= maxAttempts) {
      this.blockedUsers.set(emailKey, now + blockDuration);
      this.userAttempts.delete(emailKey); // Clear attempts after blocking
      return true;
    }

    return false;
  }

  // Record a failed login attempt for an IP (general protection)
  recordFailedIPAttempt(ip) {
    const now = Date.now();
    const maxAttempts = 20; // Higher limit for IP-based blocking
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const blockDuration = 60 * 60 * 1000; // 1 hour

    if (!this.ipAttempts.has(ip)) {
      this.ipAttempts.set(ip, {
        count: 1,
        firstAttempt: now,
        lastAttempt: now
      });
      return false; // Not blocked yet
    }

    const attempts = this.ipAttempts.get(ip);

    // Reset if window has passed
    if (now - attempts.firstAttempt > windowMs) {
      this.ipAttempts.set(ip, {
        count: 1,
        firstAttempt: now,
        lastAttempt: now
      });
      return false;
    }

    // Increment attempt count
    attempts.count++;
    attempts.lastAttempt = now;

    // Block if too many attempts
    if (attempts.count >= maxAttempts) {
      this.blockedIPs.set(ip, now + blockDuration);
      this.ipAttempts.delete(ip); // Clear attempts after blocking
      return true;
    }

    return false;
  }

  // Record a successful login (clear attempts for both user and IP)
  recordSuccessfulLogin(email, ip) {
    if (email) {
      this.userAttempts.delete(email.toLowerCase());
    }
    if (ip) {
      this.ipAttempts.delete(ip);
    }
  }

  // Get remaining attempts before block for user
  getRemainingUserAttempts(email) {
    if (!email) return 5;
    const maxAttempts = 5;
    const attempts = this.userAttempts.get(email.toLowerCase());
    if (!attempts) return maxAttempts;
    return Math.max(0, maxAttempts - attempts.count);
  }

  // Get remaining attempts before block for IP
  getRemainingIPAttempts(ip) {
    const maxAttempts = 20;
    const attempts = this.ipAttempts.get(ip);
    if (!attempts) return maxAttempts;
    return Math.max(0, maxAttempts - attempts.count);
  }

  // Get time until unblock for user (in minutes)
  getTimeUntilUserUnblock(email) {
    if (!email) return 0;
    const blockedUntil = this.blockedUsers.get(email.toLowerCase());
    if (!blockedUntil) return 0;
    const remaining = blockedUntil - Date.now();
    return Math.ceil(remaining / (60 * 1000)); // Convert to minutes
  }

  // Get time until unblock for IP (in minutes)
  getTimeUntilIPUnblock(ip) {
    const blockedUntil = this.blockedIPs.get(ip);
    if (!blockedUntil) return 0;
    const remaining = blockedUntil - Date.now();
    return Math.ceil(remaining / (60 * 1000)); // Convert to minutes
  }
}

const rateLimiter = new RateLimiter();

// Clean up every hour
setInterval(() => {
  rateLimiter.cleanup();
}, 60 * 60 * 1000);

// Middleware for login rate limiting
const loginRateLimit = (req, res, next) => {
  const ip = req.ip || req.connection.remoteAddress;
  const email = req.body?.email;

  // Check IP-based blocking first (for general protection against brute force)
  if (rateLimiter.isIPBlocked(ip)) {
    const timeUntilUnblock = rateLimiter.getTimeUntilIPUnblock(ip);
    return res.status(429).json({
      message: `Too many login attempts from this location. Please try again in ${timeUntilUnblock} minutes.`,
      retryAfter: timeUntilUnblock,
      blockType: 'IP'
    });
  }

  // Check user-based blocking (if email is provided)
  if (email && rateLimiter.isUserBlocked(email)) {
    const timeUntilUnblock = rateLimiter.getTimeUntilUserUnblock(email);
    return res.status(429).json({
      message: `Too many failed login attempts for this account. Please try again in ${timeUntilUnblock} minutes.`,
      retryAfter: timeUntilUnblock,
      blockType: 'USER'
    });
  }

  // Add rate limiter to request for use in login controller
  req.rateLimiter = rateLimiter;
  req.clientIP = ip;
  req.userEmail = email;
  next();
};

// Middleware for general API rate limiting
const apiRateLimit = (req, res, next) => {
  // Simple rate limiting for API calls
  const ip = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxRequests = 100; // 100 requests per minute

  if (!rateLimiter.apiRequests) {
    rateLimiter.apiRequests = new Map();
  }

  const requests = rateLimiter.apiRequests.get(ip) || [];
  const recentRequests = requests.filter(time => now - time < windowMs);

  if (recentRequests.length >= maxRequests) {
    return res.status(429).json({
      message: 'Too many requests. Please try again later.',
      retryAfter: Math.ceil(windowMs / 1000)
    });
  }

  recentRequests.push(now);
  rateLimiter.apiRequests.set(ip, recentRequests);

  next();
};

module.exports = {
  loginRateLimit,
  apiRateLimit,
  rateLimiter
};
