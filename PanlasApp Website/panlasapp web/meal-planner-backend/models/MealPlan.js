const mongoose = require('mongoose');

const mealPlanSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  date: {
    type: String,
    required: true
  },
  isTemplate: {
    type: Boolean,
    default: false
  },
  templateName: {
    type: String
  },
  // Rice bowls requested by user
  riceBowls: {
    type: Number,
    default: 0,
    min: 0
  },
  // For website compatibility - meals array with references
  meals: [{
    meal: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Meal'
    },
    mealType: String, // breakfast, lunch, dinner, snack
    portions: {
      type: Number,
      default: 1
    },
    completed: {
      type: Boolean,
      default: false
    }
  }],
  breakfast: [{
    name: String,
    mealType: [String],
    category: [String],
    dietaryTags: [String],
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number,
    image: String,
    description: String,
    ingredients: [String],
    instructions: [String],
    instanceId: String, // Unique identifier for this instance of the meal
    portions: {
      type: Number,
      default: 1
    }
  }],
  lunch: [{
    name: String,
    mealType: [String],
    category: [String],
    dietaryTags: [String],
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number,
    image: String,
    description: String,
    ingredients: [String],
    instructions: [String],
    instanceId: String,
    portions: {
      type: Number,
      default: 1
    }
  }],
  dinner: [{
    name: String,
    mealType: [String],
    category: [String],
    dietaryTags: [String],
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number,
    image: String,
    description: String,
    ingredients: [String],
    instructions: [String],
    instanceId: String,
    portions: {
      type: Number,
      default: 1
    }
  }],
  snack: [{
    name: String,
    mealType: [String],
    category: [String],
    dietaryTags: [String],
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number,
    image: String,
    description: String,
    ingredients: [String],
    instructions: [String],
    instanceId: String,
    portions: {
      type: Number,
      default: 1
    }
  }],
  mealTimes: {
    breakfast: String,
    lunch: String,
    dinner: String,
    snack: String
  },
  isLocked: {
    type: Boolean,
    default: false
  },
  completedMeals: {
    breakfast: [String], // Array of instanceIds
    lunch: [String],
    dinner: [String],
    snack: [String]
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create a compound index on user and date for faster lookups
mealPlanSchema.index({ user: 1, date: 1 }, { unique: true });

const MealPlan = mongoose.model('MealPlan', mealPlanSchema);

module.exports = MealPlan;
