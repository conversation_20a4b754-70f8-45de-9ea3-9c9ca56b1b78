// Base API service for making requests to the backend

// Get API base URL from environment variables with fallback
const getApiBaseUrl = () => {
  // In production, use the environment variable
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }

  // In development, check if we're running locally
  if (import.meta.env.DEV) {
    return 'http://localhost:5000/api';
  }

  // Fallback for production if env var not set
  return '/api';
};

const API_BASE_URL = getApiBaseUrl();

// Helper function to check if user is authenticated
const isAuthenticated = () => {
  return !!localStorage.getItem('token');
};

// Main API request function with improved error handling
const apiRequest = async (endpoint, options = {}) => {
  try {
    const token = localStorage.getItem('token');
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`;
    }
    
    const config = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...(options.headers || {})
      }
    };
    
    console.log(`Making ${options.method || 'GET'} request to ${API_BASE_URL}${endpoint}`);
    console.log('Request config:', {
      method: options.method || 'GET',
      headers: config.headers,
      body: options.body ? JSON.parse(options.body) : undefined
    });
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    
    console.log(`Response status: ${response.status}`);
    
    if (!response.ok) {
      let errorMessage = `API error: ${response.status}`;
      try {
        const errorData = await response.json();
        console.error('API error response:', errorData);
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        console.error('Could not parse error response as JSON');
      }
      throw new Error(errorMessage);
    }
    
    const data = await response.json();
    console.log('API response data:', data);
    return data;
  } catch (error) {
    console.error(`API request error for ${endpoint}:`, error);
    throw error;
  }
};

// Meal plan related API functions
const getMealPlans = async () => {
  console.log('Getting all meal plans');
  return apiRequest('/meal-plans');
};

const getMealPlanByDate = async (date) => {
  // Format the date if it's a Date object
  const formattedDate = typeof date === 'object' ? formatDate(date) : date;
  console.log(`Getting meal plan for date: ${formattedDate}`);
  return apiRequest(`/meal-plans/date/${formattedDate}`);
};

const createOrUpdateMealPlan = async (date, mealType, meal) => {
  console.log(`Creating/updating meal plan for date: ${date}, meal type: ${mealType}`);
  return apiRequest('/meal-plans', {
    method: 'POST',
    body: JSON.stringify({ date, mealType, meal })
  });
};

const toggleLockMealPlan = async (date, isLocked) => {
  if (!isAuthenticated()) {
    console.log('User not authenticated, skipping lock toggle');
    return null;
  }
  
  // Format the date correctly
  const formattedDate = typeof date === 'object' ? formatDate(date) : date;
  
  console.log(`Toggling lock for date ${formattedDate} to ${isLocked}`);
  
  try {
    const response = await apiRequest(`/meal-plans/${formattedDate}/lock`, {
      method: 'PUT',
      body: JSON.stringify({ isLocked })
    });
    
    console.log('Toggle lock response:', response);
    return response;
  } catch (error) {
    console.error(`Error toggling lock for date ${formattedDate}:`, error);
    throw error;
  }
};

const markMealCompleted = async (date, mealType, mealInstanceId, isCompleted) => {
  console.log(`Marking meal as ${isCompleted ? 'completed' : 'incomplete'} for date: ${date}, meal type: ${mealType}, instance: ${mealInstanceId}`);
  return apiRequest(`/meal-plans/complete/${date}`, {
    method: 'PATCH',
    body: JSON.stringify({ mealType, mealInstanceId, isCompleted })
  });
};

const removeMealFromPlan = async (date, mealType, mealInstanceId) => {
  console.log(`Removing meal from plan for date: ${date}, meal type: ${mealType}, instance: ${mealInstanceId}`);
  return apiRequest(`/meal-plans/remove/${date}`, {
    method: 'PATCH',
    body: JSON.stringify({ mealType, mealInstanceId })
  });
};

const updateMealTimes = async (date, mealTimes) => {
  console.log(`Updating meal times for date: ${date}`, mealTimes);
  return apiRequest(`/meal-plans/meal-times/${date}`, {
    method: 'PATCH',
    body: JSON.stringify(mealTimes)
  });
};

const deleteMealPlan = async (date) => {
  console.log(`Deleting meal plan for date: ${date}`);
  return apiRequest('/meal-plans', {
    method: 'DELETE',
    body: JSON.stringify({ date })
  });
};

// Create meal plan from template
const createFromTemplate = async (templateId, date) => {
  console.log(`Creating meal plan from template ${templateId} for date ${date}`);
  return apiRequest('/meal-plans/from-template', {
    method: 'POST',
    body: JSON.stringify({ templateId, date })
  });
};

// Helper function to format date as YYYY-MM-DD
const formatDate = (date) => {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Export all functions as a default export for ES modules
export default {
  isAuthenticated,
  getMealPlans,
  getMealPlanByDate,
  createOrUpdateMealPlan,
  toggleLockMealPlan,
  markMealCompleted,
  removeMealFromPlan,
  updateMealTimes,
  deleteMealPlan,
  createFromTemplate,
  formatDate
};
